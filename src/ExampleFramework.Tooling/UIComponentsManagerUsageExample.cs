using System;
using System.IO;
using System.Threading.Tasks;

namespace ExampleFramework.Tooling;

/// <summary>
/// Example usage of the enhanced UIComponentsManager that can work with .sln and .csproj files.
/// </summary>
public static class UIComponentsManagerUsageExample
{
    /// <summary>
    /// Example: Analyze a solution file for UI components
    /// </summary>
    public static async Task AnalyzeSolutionExample()
    {
        try
        {
            // Path to your solution file
            string solutionPath = @"C:\YourProject\YourSolution.sln";
            
            // Create UIComponentsManager from solution
            var manager = await UIComponentsManager.CreateFromSolutionAsync(
                solutionPath,
                requireExampleFrameworkAssemblyPresent: true,
                includeApparentUIComponentsWithNoExamples: false);

            // Access the discovered UI components
            Console.WriteLine($"Found {manager.UIComponents.Count()} UI components:");
            
            foreach (var component in manager.SortedUIComponents)
            {
                Console.WriteLine($"- {component.DisplayName} ({component.Kind})");
                
                foreach (var example in component.Examples)
                {
                    Console.WriteLine($"  Example: {example.DisplayName}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error analyzing solution: {ex.Message}");
        }
    }

    /// <summary>
    /// Example: Analyze a single project file for UI components
    /// </summary>
    public static async Task AnalyzeProjectExample()
    {
        try
        {
            // Path to your project file
            string projectPath = @"C:\YourProject\YourProject.csproj";
            
            // Create UIComponentsManager from project
            var manager = await UIComponentsManager.CreateFromProjectAsync(
                projectPath,
                requireExampleFrameworkAssemblyPresent: false,
                includeApparentUIComponentsWithNoExamples: true);

            // Check if ExampleFramework is referenced
            if (manager.ReferencesExampleFrameworkAssembly)
            {
                Console.WriteLine("Project references ExampleFramework assembly");
            }

            // Access the discovered UI components
            Console.WriteLine($"Found {manager.UIComponents.Count()} UI components:");
            
            foreach (var component in manager.SortedUIComponents)
            {
                Console.WriteLine($"- {component.DisplayName} ({component.Kind})");
                Console.WriteLine($"  Examples: {component.Examples.Count}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error analyzing project: {ex.Message}");
        }
    }

    /// <summary>
    /// Example: Analyze multiple projects for UI components
    /// </summary>
    public static async Task AnalyzeMultipleProjectsExample()
    {
        try
        {
            // Paths to your project files
            var projectPaths = new[]
            {
                @"C:\YourProject\Project1.csproj",
                @"C:\YourProject\Project2.csproj",
                @"C:\YourProject\Project3.csproj"
            };
            
            // Create UIComponentsManager from multiple projects
            var manager = await UIComponentsManager.CreateFromProjectsAsync(
                projectPaths,
                requireExampleFrameworkAssemblyPresent: false,
                includeApparentUIComponentsWithNoExamples: true);

            // Access the discovered UI components from all projects
            Console.WriteLine($"Found {manager.UIComponents.Count()} UI components across {projectPaths.Length} projects:");
            
            foreach (var component in manager.SortedUIComponents)
            {
                Console.WriteLine($"- {component.DisplayName} ({component.Kind})");
                
                if (component.Examples.Any())
                {
                    Console.WriteLine($"  Examples:");
                    foreach (var example in component.Examples)
                    {
                        Console.WriteLine($"    - {example.DisplayName} {(example.IsAutoGenerated ? "(auto-generated)" : "")}");
                    }
                }
            }

            // Access categories if any
            if (manager.Categories.Any())
            {
                Console.WriteLine("\nCategories:");
                foreach (var category in manager.Categories)
                {
                    Console.WriteLine($"- {category.Name}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error analyzing projects: {ex.Message}");
        }
    }

    /// <summary>
    /// Example: Working with the original Compilation-based constructor
    /// </summary>
    public static async Task WorkWithCompilationExample()
    {
        try
        {
            // If you already have a Roslyn Compilation object, you can still use the original constructor
            string projectPath = @"C:\YourProject\YourProject.csproj";
            
            // Build compilation manually using MSBuild workspace
            UIComponentsManager.EnsureMSBuildLocated(); // This would need to be made public or use reflection
            
            using var workspace = Microsoft.CodeAnalysis.MSBuild.MSBuildWorkspace.Create();
            var project = await workspace.OpenProjectAsync(projectPath);
            var compilation = await project.GetCompilationAsync();

            if (compilation != null)
            {
                // Use the original constructor
                var manager = new UIComponentsManager(
                    compilation,
                    requireExampleFrameworkAssemblyPresent: true,
                    includeApparentUIComponentsWithNoExamples: false);

                Console.WriteLine($"Found {manager.UIComponents.Count()} UI components using direct compilation");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error working with compilation: {ex.Message}");
        }
    }
}
